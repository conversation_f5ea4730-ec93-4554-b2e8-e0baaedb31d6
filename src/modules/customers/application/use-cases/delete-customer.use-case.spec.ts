import { Test, TestingModule } from '@nestjs/testing';
import { DeleteCustomerUseCase } from './delete-customer.use-case';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';
import { NotFoundException } from '@nestjs/common';
import { CustomerStatus } from '../../domain/entities/customer.entity';
import { UserRepository } from '@/core/ports/repositories/user-repository.interface';
import { KeycloakIdentityProviderService } from '@/infrastructure/keycloak/keycloak-identity-provider.service';
import { User } from '@/core/domain/user.entity';
import { Role } from '@/core/domain/role.enum';

describe('DeleteCustomerUseCase', () => {
  let useCase: DeleteCustomerUseCase;
  let customerRepository: jest.Mocked<ICustomerRepository>;
  let userRepository: jest.Mocked<UserRepository>;
  let keycloakService: jest.Mocked<KeycloakIdentityProviderService>;

  beforeEach(async () => {
    const mockUserRepository: jest.Mocked<UserRepository> = {
      findAll: jest.fn(),
      findById: jest.fn(),
      findByIds: jest.fn(),
      findByEmail: jest.fn(),
      findByKeycloakId: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      updateUserKeycloakId: jest.fn(),
    };

    const mockKeycloakService = {
      registerUser: jest.fn(),
      deleteUser: jest.fn(),
      assignUserRoles: jest.fn(),
      authenticate: jest.fn(),
      refreshToken: jest.fn(),
      logout: jest.fn(),
      getUserInfo: jest.fn(),
    } as unknown as jest.Mocked<KeycloakIdentityProviderService>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeleteCustomerUseCase,
        {
          provide: 'ICustomerRepository',
          useValue: {
            findByUuid: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: 'UserRepository',
          useValue: mockUserRepository,
        },
        {
          provide: KeycloakIdentityProviderService,
          useValue: mockKeycloakService,
        },
      ],
    }).compile();

    useCase = module.get(DeleteCustomerUseCase);
    customerRepository = module.get('ICustomerRepository');
    userRepository = module.get<jest.Mocked<UserRepository>>('UserRepository');
    keycloakService = module.get<jest.Mocked<KeycloakIdentityProviderService>>(KeycloakIdentityProviderService);
  });

  it('should delete customer if found', async () => {
    customerRepository.findByUuid.mockResolvedValue({
      uuid: '123',
      razaoSocial: 'Test',
      cnpj: 'doc',
      email: '<EMAIL>',
      status: CustomerStatus.ACTIVE,
      userId: 'user1',
      url: 'https://test.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    await expect(useCase.execute('123')).resolves.toBeUndefined();
    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(customerRepository.delete).toHaveBeenCalledWith('123');
  });

  it('should delete customer and associated user from Keycloak when user has keycloakId', async () => {
    const mockUser = new User(
      'user1',
      '<EMAIL>',
      'Test User',
      'password',
      Role.USER,
      new Date(),
      new Date(),
      'keycloak-user-id'
    );

    customerRepository.findByUuid.mockResolvedValue({
      uuid: '123',
      razaoSocial: 'Test',
      cnpj: 'doc',
      email: '<EMAIL>',
      status: CustomerStatus.ACTIVE,
      userId: 'user1',
      url: 'https://test.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    userRepository.findById.mockResolvedValue(mockUser);
    keycloakService.deleteUser.mockResolvedValue();

    await expect(useCase.execute('123')).resolves.toBeUndefined();

    expect(userRepository.findById).toHaveBeenCalledWith('user1');
    expect(keycloakService.deleteUser).toHaveBeenCalledWith('keycloak-user-id');
    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(customerRepository.delete).toHaveBeenCalledWith('123');
  });

  it('should continue deletion even if Keycloak deletion fails', async () => {
    const mockUser = new User(
      'user1',
      '<EMAIL>',
      'Test User',
      'password',
      Role.USER,
      new Date(),
      new Date(),
      'keycloak-user-id'
    );

    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

    customerRepository.findByUuid.mockResolvedValue({
      uuid: '123',
      razaoSocial: 'Test',
      cnpj: 'doc',
      email: '<EMAIL>',
      status: CustomerStatus.ACTIVE,
      userId: 'user1',
      url: 'https://test.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    userRepository.findById.mockResolvedValue(mockUser);
    keycloakService.deleteUser.mockRejectedValue(new Error('Keycloak error'));

    await expect(useCase.execute('123')).resolves.toBeUndefined();

    expect(userRepository.findById).toHaveBeenCalledWith('user1');
    expect(keycloakService.deleteUser).toHaveBeenCalledWith('keycloak-user-id');
    expect(consoleSpy).toHaveBeenCalledWith(
      'Erro ao deletar usuário do customer 123 no Keycloak:',
      expect.any(Error)
    );
    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(customerRepository.delete).toHaveBeenCalledWith('123');

    consoleSpy.mockRestore();
  });

  it('should throw NotFoundException if customer not found', async () => {
    customerRepository.findByUuid.mockResolvedValue(null);
    await expect(useCase.execute('notfound')).rejects.toThrow(
      NotFoundException,
    );
    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(customerRepository.delete).not.toHaveBeenCalled();
  });
});
