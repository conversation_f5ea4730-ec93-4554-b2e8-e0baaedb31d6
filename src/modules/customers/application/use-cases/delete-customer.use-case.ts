import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';
import { UserRepository } from '@/core/ports/repositories/user-repository.interface';
import { KeycloakIdentityProviderService } from '@/infrastructure/keycloak/keycloak-identity-provider.service';

@Injectable()
export class DeleteCustomerUseCase {
  constructor(
    @Inject('ICustomerRepository')
    private readonly customerRepository: ICustomerRepository,
    @Inject('UserRepository')
    private readonly userRepository: UserRepository,
    private readonly keycloakIdentityProvider: KeycloakIdentityProviderService,
  ) {}

  async execute(uuid: string): Promise<void> {
    const customer = await this.customerRepository.findByUuid(uuid);
    if (!customer) {
      throw new NotFoundException('Cliente não encontrado.');
    }

    // Se o customer tem um usuário associado, deletar também no Keycloak
    if (customer.userId) {
      try {
        const user = await this.userRepository.findById(customer.userId);
        if (user && user.keycloakId) {
          await this.keycloakIdentityProvider.deleteUser(user.keycloakId);
        }
      } catch (error) {
        // Log do erro mas não falha a operação se o usuário não existir no Keycloak
        console.warn(`Erro ao deletar usuário do customer ${uuid} no Keycloak:`, error);
      }
    }

    await this.customerRepository.delete(uuid);
  }
}
