import { Modu<PERSON> } from '@nestjs/common';
import { Employee<PERSON>ontroller } from './employee.controller';
import { EmployeeService } from './employee.service';
import { CreateEmployeeUseCase } from '@/core/application/use-cases/employee/create-employee.use-case';
import { PrismaEmployeeRepository } from '@/infrastructure/repositories/prisma-employee.repository';
import { PrismaEventPublisherService } from '@/infrastructure/events/prisma-event-publisher.service';
import { PrismaModule } from '@/infrastructure/prisma/prisma.module';
import { EMPLOYEE_REPOSITORY } from '@/core/ports/repositories/employee-repository.port';
import { ListEmployeeUseCase } from '@/core/application/use-cases/employee/list-employee.use-case';
import { UpdateEmployeeUseCase } from '@/core/application/use-cases/employee/update-employee.use-case';
import { ListEmployeeByUuidUseCase } from '@/core/application/use-cases/employee/list-employee-by-uuid.use-case';
import { DeleteEmployeeUseCase } from '@/core/application/use-cases/employee/delete-employee.use-case';
import { CreateEmployeeArchiveUseCase } from '@/core/application/use-cases/employee/create-employee-archive.use-case';
import { FindEmployeeArchiveByUuidUseCase } from '@/core/application/use-cases/employee/find-employee-archive-by-uuid.use-case';
import { FindOneEmployeeArchiveUseCase } from '@/core/application/use-cases/employee/find-one-employee-archive.use-case';
import { DeleteEmployeeArchiveUseCase } from '@/core/application/use-cases/employee/delete-employee-archive.use-case';
import { PrismaArchiveRepository } from '@/infrastructure/repositories/prisma-archive.repository';
import { ARCHIVE_REPOSITORY } from '@/core/ports/repositories/archive-repository.port';
import { AwsModule } from '@/infrastructure/aws/aws.module';
import { MulterModule } from '@nestjs/platform-express';
import { S3StorageProvider } from '@/infrastructure/aws/s3/s3-storage.provider';
import { UsersModule } from '@/modules/users/users.module';
import { AuthModule } from '@/modules/auth/auth.module';
import { KeycloakModule } from '@/infrastructure/keycloak/keycloak.module';
import { forwardRef } from '@nestjs/common';
import { ListPersonalDocumentsByEmployeeUuidUseCase } from '@/core/application/use-cases/employee/list-personal-documents-by-employee-uuid.use-case';
import { UploadEmployeePersonalDocumentUseCase } from '@/core/application/use-cases/employee/upload-employee-personal-document.use-case';
import { DeleteEmployeePersonalDocumentUseCase } from '@/core/application/use-cases/employee/delete-employee-personal-document.use-case';

export const EVENT_PUBLISHER = 'EVENT_PUBLISHER';

@Module({
  imports: [PrismaModule, AwsModule, MulterModule.register({
    limits: {
      fileSize: 20 * 1024 * 1024,
    },
  }), UsersModule, KeycloakModule, forwardRef(() => AuthModule)],
  controllers: [EmployeeController],
  providers: [
    CreateEmployeeUseCase,
    ListEmployeeUseCase,
    UpdateEmployeeUseCase,
    ListEmployeeByUuidUseCase,
    DeleteEmployeeUseCase,
    ListEmployeeByUuidUseCase,
    CreateEmployeeArchiveUseCase,
    FindEmployeeArchiveByUuidUseCase,
    FindOneEmployeeArchiveUseCase,
    DeleteEmployeeArchiveUseCase,
    UploadEmployeePersonalDocumentUseCase,
    ListPersonalDocumentsByEmployeeUuidUseCase,
    EmployeeService,
    DeleteEmployeePersonalDocumentUseCase,
      {
      provide: EMPLOYEE_REPOSITORY,
      useClass: PrismaEmployeeRepository,
    },
    {
      provide: ARCHIVE_REPOSITORY,
      useClass: PrismaArchiveRepository,
    },
    {
      provide: EVENT_PUBLISHER,
      useClass: PrismaEventPublisherService,
    }, {
      provide: 'IStorageProvider',
      useExisting: S3StorageProvider,
    },
  ],
  exports: [
    EMPLOYEE_REPOSITORY,
    ARCHIVE_REPOSITORY,
    EVENT_PUBLISHER,
    EmployeeService,
    CreateEmployeeUseCase,
    ListEmployeeUseCase,
    UpdateEmployeeUseCase,
    ListEmployeeByUuidUseCase,
    DeleteEmployeeUseCase,
    ListEmployeeByUuidUseCase,
    CreateEmployeeArchiveUseCase,
    FindEmployeeArchiveByUuidUseCase,
    FindOneEmployeeArchiveUseCase,
    DeleteEmployeeArchiveUseCase,
    ListPersonalDocumentsByEmployeeUuidUseCase,
    UploadEmployeePersonalDocumentUseCase,
    DeleteEmployeePersonalDocumentUseCase,
    EmployeeService,
  ],
})
export class EmployeeModule { }
