import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import {
  EMPLOYEE_REPOSITORY,
  EmployeeRepositoryPort,
} from '@/core/ports/repositories/employee-repository.port';
import { UserRepository } from '@/core/ports/repositories/user-repository.interface';
import { KeycloakIdentityProviderService } from '@/infrastructure/keycloak/keycloak-identity-provider.service';

export interface DeleteEmployeeInput {
  uuid: string;
}

@Injectable()
export class DeleteEmployeeUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepositoryPort,
    @Inject('UserRepository')
    private readonly userRepository: UserRepository,
    private readonly keycloakIdentityProvider: KeycloakIdentityProviderService,
  ) {}

  async execute(input: DeleteEmployeeInput): Promise<void> {
    const existingEmployee = await this.employeeRepository.findByUuid(
      input.uuid,
    );

    if (!existingEmployee) {
      throw new NotFoundException('Colaborador não encontrado');
    }

    // Se o employee tem um usuário associado, deletar também no Keycloak
    if (existingEmployee.userId) {
      try {
        const user = await this.userRepository.findById(existingEmployee.userId);
        if (user && user.keycloakId) {
          await this.keycloakIdentityProvider.deleteUser(user.keycloakId);
        }
      } catch (error) {
        // Log do erro mas não falha a operação se o usuário não existir no Keycloak
        console.warn(`Erro ao deletar usuário do employee ${input.uuid} no Keycloak:`, error);
      }
    }

    await this.employeeRepository.delete(input.uuid);
  }
}
